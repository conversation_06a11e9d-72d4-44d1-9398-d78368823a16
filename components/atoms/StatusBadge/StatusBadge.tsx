'use client';

import React from 'react';
import { Bot } from 'lucide-react'; // Changed from Sparkles to Bot

export type UserStatus = 'active' | 'inactive' | 'pending' | 'suspended' | 'error';

export interface StatusBadgeProps {
  status: UserStatus;
  className?: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className = '' }) => {
  const getStatusStyles = () => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border border-green-200';
      case 'inactive':
        return 'bg-section-bg-neutral-alt text-text-secondary border border-gray-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border border-yellow-200';
      case 'suspended':
        return 'bg-red-100 text-red-800 border border-red-200';
      case 'error':
        return 'bg-red-100 text-red-800 border border-red-200'; // Similar to suspended for error state
      default:
        return 'bg-section-bg-neutral-alt text-text-primary border border-gray-200';
    }
  };

  return (
    <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium ${getStatusStyles()} ${className}`}>
      {status === 'pending' && <Bot size={14} className="animate-pulse mr-1.5 text-yellow-600" />} 
      {status === 'pending' ? 'Generating...' : status}
    </span>
  );
};
