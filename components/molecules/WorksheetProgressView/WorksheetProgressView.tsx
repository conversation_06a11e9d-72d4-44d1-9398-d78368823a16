'use client';

import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import { LoadingWorksheetScreen } from '@/components/molecules/LoadingWorksheetScreen/LoadingWorksheetScreen';
import QuestionListingView from '@/components/molecules/QuestionListingView/QuestionListingView';
import { Button } from '@/components/atoms/Button/Button';
import Icon from '@/components/atoms/Icon';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { WorksheetGeneratingStatus } from '@/apis/worksheet';
import { ProgressData } from '@/components/molecules/ProgressBar/ProgressBar';
import { useWorksheetProgress } from '@/hooks/useWorksheetProgress';
import { useState } from 'react';
import { PrintModal } from '@/components/molecules/PrintModal';
import { QuestionReorderModal } from '@/components/molecules/QuestionReorderModal/QuestionReorderModal';
import { reorderWorksheetQuestionsAction, removeQuestionFromWorksheetAction, bulkRemoveQuestionsAction } from '@/actions/worksheet.action';
import { Breadcrumb } from '@/components/atoms/Breadcrumb/Breadcrumb';
import { useSession } from 'next-auth/react';
import { EUserRole } from '@/config/enums/user';
import { HomeIcon, ClipboardListIcon } from 'lucide-react';
import { cn } from '@/utils/cn';


type WorksheetProgressViewProps = {
  worksheetId: string;
  initialStatus: WorksheetGeneratingStatus;
  initialQuestions?: Question[];
  initialQuestionIds?: string[]; // Array of question IDs from API
  initialProgress?: ProgressData;
  worksheetInfo?: {
    topic?: string;
    subject?: string; // Added subject
    grade?: string;
    language?: string;
    level?: string;
    totalQuestions?: number;
  };
  schoolInfo?: {
    name: string;
    address?: string;
    phoneNumber?: string;
    registeredNumber?: string;
    email?: string;
    logoUrl?: string;
  };
};

export const WorksheetProgressView: React.FC<WorksheetProgressViewProps> = ({
  worksheetId,
  initialStatus,
  initialQuestions = [],
  initialQuestionIds = [],
  initialProgress,
  worksheetInfo,
  schoolInfo,
}) => {
  const router = useRouter();
  const { status, questions, setQuestions } = useWorksheetProgress({
    worksheetId,
    initialStatus,
    initialQuestions,
    initialProgress,
  });

  // State for print modal and reorder modal
  const [isPrintModalOpen, setIsPrintModalOpen] = useState(false);
  const [isReorderModalOpen, setIsReorderModalOpen] = useState(false);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  // Validate question structure to prevent PDF generation errors
  const validateQuestion = (question: Question): Question => {
    return {
      id: question.id || undefined,
      type: question.type || 'multiple_choice',
      content: question.content || '',
      image: question.image || null,
      svgCode: question.svgCode || undefined,
      imagePrompt: question.imagePrompt || null,
      options: Array.isArray(question.options) ? question.options : [],
      answer: Array.isArray(question.answer) ? question.answer : [],
      explain: question.explain || '',
      prompt: question.prompt || undefined,
      subject: question.subject || undefined,
    };
  };

  // Validate questions array to ensure all questions have proper structure
  const validateQuestions = (questions: Question[]): Question[] => {
    if (!Array.isArray(questions)) {
      console.warn('Questions is not an array, returning empty array');
      return [];
    }

    return questions
      .filter(question => question && typeof question === 'object')
      .map(validateQuestion);
  };

  // Handle saving reordered questions to backend
  const handleSaveReorder = async (reorderedQuestions: Question[], questionIds?: string[]) => {
    if (!worksheetId) {
      throw new Error('Worksheet ID is required');
    }

    try {
      // Validate questions before processing
      const validatedQuestions = validateQuestions(reorderedQuestions);

      if (validatedQuestions.length !== reorderedQuestions.length) {
        console.warn('Some questions were filtered out during validation');
      }

      // Create reorder data - prioritize questionIds from API, then question.id, then fallback to index
      const reorderData = {
        reorders: validatedQuestions.map((question, index) => {
          let questionId: string;

          if (questionIds && questionIds[index]) {
            // Use questionIds from API if available
            questionId = questionIds[index];
          } else if (question.id) {
            // Fallback to question.id if available
            questionId = question.id;
          } else {
            // Last resort: use array index
            questionId = index.toString();
          }

          return {
            questionId,
            newPosition: index + 1 // API expects 1-based positions
          };
        })
      };

      const result = await reorderWorksheetQuestionsAction(worksheetId, reorderData);

      if (result.status === 'error') {
        throw new Error(result.message || 'Failed to reorder questions');
      }

      // Update the parent state with the validated questions
      setQuestions(validatedQuestions);
    } catch (error) {
      console.error('Failed to save question order:', error);
      throw error; // Re-throw to let the component handle the error
    }
  };

  // Handle single question deletion
  const handleDeleteQuestion = async (questionId: string, questionIndex: number) => {
    try {
      const result = await removeQuestionFromWorksheetAction(worksheetId, questionId);

      if (result.status === 'error') {
        throw new Error(result.message || 'Failed to delete question');
      }

      // Remove the question from local state
      setQuestions(prevQuestions => prevQuestions.filter(q => q.id !== questionId));

      // Force a router refresh to ensure cache is updated
      router.refresh();
    } catch (error) {
      console.error('Failed to delete question:', error);
      throw error;
    }
  };

  // Handle bulk question deletion
  const handleBulkDeleteQuestions = async (questionIds: string[]) => {
    try {
      const result = await bulkRemoveQuestionsAction(worksheetId, { questionIds });

      if (result.status === 'error') {
        throw new Error(result.message || 'Failed to delete questions');
      }

      // Remove the questions from local state
      setQuestions(prevQuestions => prevQuestions.filter(q => !questionIds.includes(q.id || '')));

      // Force a router refresh to ensure cache is updated
      router.refresh();
    } catch (error) {
      console.error('Failed to bulk delete questions:', error);
      throw error;
    }
  };


  // If status is pending, show the loading screen with progress
  if (status === WorksheetGeneratingStatus.PENDING) {
    return (
      <LoadingWorksheetScreen
        worksheetId={worksheetId}
        initialQuestions={initialQuestions}
        initialProgress={initialProgress}
      />
    );
  }


  // If status is generated or error, show the completed worksheet
  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex-1 overflow-auto">
        <div className="w-full max-w-5xl mx-auto">
          <div className="w-full">
            {status === WorksheetGeneratingStatus.ERROR ? (
              <div className="w-full p-8 text-center">
                <div className="text-error text-xl mb-4">
                  An error occurred while generating the worksheet
                </div>
                <div className="text-gray-500">
                  Please try again or contact support if the problem persists.
                </div>
              </div>
            ) : (
              <>
                <QuestionListingView
                  questions={questions}
                  questionIds={initialQuestionIds}
                  containerClass="pb-[50px] lg:pb-20"
                  isHtmlContent
                  worksheetInfo={worksheetInfo}
                  allowReordering={true}
                  worksheetId={worksheetId}
                  onSaveReorder={handleSaveReorder}
                  allowDelete={true}
                  allowBulkDelete={true}
                  onDeleteQuestion={handleDeleteQuestion}
                  onBulkDeleteQuestions={handleBulkDeleteQuestions}
                  isSelectionModeControlled={isSelectionMode}
                  onSelectionModeChange={setIsSelectionMode}
                />
                {/* Mobile-Responsive Bottom Action Bar - Minimal Design */}
                <div className="fixed bottom-[55px] left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-40 lg:bottom-0 lg:left-[310px] lg:w-[calc(100vw-310px)]">
                  <div className="flex justify-between items-center h-[48px] lg:h-[60px] px-3 lg:px-6">
                    <div className="w-7 h-7 lg:w-9 lg:h-9 rounded-full bg-gray-100 flex items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors">
                      <Link href="/manage-worksheet">
                        <Icon
                          variant="chevron-down"
                          className="rotate-90"
                          size={2.5}
                        />
                      </Link>
                    </div>
                    <div className="flex gap-2 lg:gap-3 items-center">
                      {/* Select Questions Button */}
                      {questions && questions.length > 0 && (
                        <Button
                          onClick={() => setIsSelectionMode(!isSelectionMode)}
                          variant={isSelectionMode ? "primary" : "outline"}
                          className={cn(
                            "px-3 py-1.5 h-8 lg:px-4 lg:py-2 lg:h-10 w-fit text-xs lg:text-sm font-medium",
                            isSelectionMode
                              ? "bg-blue-600 text-white hover:bg-blue-700"
                              : "border-blue-300 !text-blue-700 hover:bg-blue-50"
                          )}
                          iconProps={{
                            variant: isSelectionMode ? "x" : "check",
                            size: 3,
                            className: isSelectionMode ? "text-white" : "text-blue-600"
                          }}
                        >
                          <span className="hidden lg:inline">
                            {isSelectionMode ? "Exit Selection" : "Select Questions"}
                          </span>
                          <span className="lg:hidden">
                            {isSelectionMode ? "Exit" : "Select"}
                          </span>
                        </Button>
                      )}

                      {/* Reorder Questions Button */}
                      {questions && questions.length > 1 && !isSelectionMode && (
                        <Button
                          onClick={() => setIsReorderModalOpen(true)}
                          variant="outline"
                          className="px-3 py-1.5 h-8 lg:px-4 lg:py-2 lg:h-10 w-fit text-xs lg:text-sm font-medium border-blue-300 !text-blue-700 hover:bg-blue-50"
                          iconProps={{
                            variant: "list",
                            size: 3,
                            className: "text-blue-600"
                          }}
                        >
                          <span className="hidden lg:inline">Reorder Questions</span>
                          <span className="lg:hidden">Reorder</span>
                        </Button>
                      )}

                      {/* Print Button */}
                      {!isSelectionMode && (
                        <Button
                          onClick={() => setIsPrintModalOpen(true)}
                          className="px-3 py-1.5 h-8 lg:px-4 lg:py-2 lg:h-10 w-fit text-xs lg:text-sm font-medium"
                        >
                          <span className="hidden lg:inline">Print</span>
                          <span className="lg:hidden">Print</span>
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Print Modal */}
                {isPrintModalOpen && (
                  <PrintModal
                    isOpen={isPrintModalOpen}
                    onClose={() => setIsPrintModalOpen(false)}
                    questions={validateQuestions(questions)}
                    worksheetInfo={worksheetInfo}
                    schoolInfo={schoolInfo}
                  />
                )}

                {/* Question Reorder Modal */}
                {isReorderModalOpen && (
                  <QuestionReorderModal
                    isOpen={isReorderModalOpen}
                    onClose={() => setIsReorderModalOpen(false)}
                    questions={questions}
                    questionIds={initialQuestionIds}
                    onSaveReorder={handleSaveReorder}
                    isLoading={false}
                  />
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
