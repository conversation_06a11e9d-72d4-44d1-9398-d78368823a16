'use client';

import React, { useState } from 'react';
import { Loader2, AlertCircle, HelpCircle, RefreshCw, X } from 'lucide-react';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';

export interface DeleteQuestionModalProps {
  isOpen: boolean;
  onClose: () => void;
  question: Question;
  questionIndex: number; // For display purposes (Question 1, Question 2, etc.)
  onConfirm: () => Promise<void>; // Callback to handle the actual replacement
}

export const DeleteQuestionModal: React.FC<DeleteQuestionModalProps> = ({
  isOpen,
  onClose,
  question,
  questionIndex,
  onConfirm,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Handle replace confirmation
  const handleReplace = async () => {
    if (!question.id) {
      setError('Question ID is missing. Cannot replace question.');
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      await onConfirm();
      setSuccess('Question replaced with similar question successfully!');
      // Close modal after a short delay
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (error: any) {
      console.error('Error replacing question:', error);
      setError(error.message || 'An unexpected error occurred while replacing the question.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get question type display name
  const getQuestionTypeDisplay = (type: string) => {
    const typeMap: Record<string, string> = {
      'multiple_choice': 'Multiple Choice',
      'single_choice': 'Single Choice',
      'true_false': 'True/False',
      'fill_in_the_blank': 'Fill in the Blank',
      'short_answer': 'Short Answer',
      'essay': 'Essay',
      'matching': 'Matching',
      'ordering': 'Ordering',
      'calculation': 'Calculation',
      'diagram': 'Diagram',
      'long_answer': 'Long Answer',
    };
    return typeMap[type] || type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Truncate content for preview
  const truncateContent = (content: string, maxLength: number = 100) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-in fade-in duration-200 lg:left-[310px] lg:w-[calc(100vw-310px)]" style={{ zIndex: 9999 }}>
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden transform transition-all duration-300 animate-in slide-in-from-bottom-4">
        {/* Header */}
        <div className="bg-blue-600 text-white p-4 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <RefreshCw className="w-5 h-5 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold">Replace Question</h3>
              <p className="text-blue-100 text-sm">Question {questionIndex + 1}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            disabled={isSubmitting}
            className="rounded-full p-1.5 bg-white/20 hover:bg-white/30 transition-colors duration-200 disabled:opacity-50"
            aria-label="Close"
          >
            <X size={16} className="text-white" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto">
          {/* Question Preview */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-sm font-medium text-gray-600">Type:</span>
              <span className="text-sm px-2 py-1 bg-green-100 text-green-700 rounded-full font-medium capitalize">
                {getQuestionTypeDisplay(question.type)}
              </span>
            </div>
            <div className="text-sm text-gray-800">
              <strong>Content:</strong> <span dangerouslySetInnerHTML={{ __html: truncateContent(question.content) }} />
            </div>
          </div>

          {/* Warning Message */}
          <div className="flex items-start space-x-3 mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-red-800">
                Are you sure you want to delete this question?
              </p>
              <p className="text-sm text-red-700 mt-1">
                This action cannot be undone. The question will be permanently removed from the worksheet.
              </p>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center">
                <AlertCircle className="w-4 h-4 text-red-600 mr-2" />
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-600 rounded-full mr-2 flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <p className="text-sm text-green-700">{success}</p>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            disabled={isSubmitting}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 transition-colors font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleDelete}
            disabled={isSubmitting || success !== null}
            className="px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700 disabled:opacity-50 flex items-center transition-colors font-medium shadow-sm"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="animate-spin mr-2" size={16} />
                Deleting...
              </>
            ) : (
              <>
                <Trash className="mr-2" size={16} />
                Delete Question
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};
