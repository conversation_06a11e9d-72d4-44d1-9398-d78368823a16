'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import {
  UserCircle,
  School,
  Edit,
  Trash2,
  Mail,
  MoreHorizontal,
  Phone,
  Calendar,
  Shield,
} from 'lucide-react';
import { EUserRole } from '@/config/enums/user';
import { MobileOptimizedTable } from '@/components/molecules/CustomTable/MobileOptimizedTable';
import { cn } from '@/utils/cn';

export interface MobileOptimizedUsersTableProps {
  users: Array<{
    id: string;
    name: string;
    email: string;
    role: EUserRole;
    schoolId?: string | null;
    status?: 'active' | 'inactive' | 'pending' | 'suspended';
    lastActivity?: string;
    phone?: string;
  }>;
  error: string | null;
  isLoading?: boolean;
  tableTitle?: string;
  entityName?: string;
  entityNamePlural?: string;
  onEditUser?: (userId: string) => void;
  onDeleteUser?: (userId: string) => void;
  hideRoleColumn?: boolean;
  hideSchoolColumn?: boolean;
  schools?: Array<{
    id: string;
    name: string;
  }>;
}

export const MobileOptimizedUsersTable: React.FC<MobileOptimizedUsersTableProps> = ({
  users,
  error,
  isLoading = false,
  tableTitle = "All Users",
  entityName = "user",
  entityNamePlural = "users",
  onEditUser,
  onDeleteUser,
  hideRoleColumn = false,
  hideSchoolColumn = false,
  schools = []
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    role: 'all',
    status: 'all',
  });

  // Filter users based on search and filters
  const filteredUsers = useMemo(() => {
    return users.filter(user => {
      const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           user.email.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesRole = filters.role === 'all' || user.role === filters.role;
      const matchesStatus = filters.status === 'all' || user.status === filters.status;
      
      return matchesSearch && matchesRole && matchesStatus;
    });
  }, [users, searchTerm, filters]);

  const getSchoolName = useCallback((schoolId?: string | null) => {
    if (!schoolId) return 'No School';
    const school = schools.find(s => s.id === schoolId);
    return school?.name || 'Unknown School';
  }, [schools]);

  const getRoleDisplay = (role: EUserRole) => {
    const roleMap = {
      [EUserRole.ADMIN]: 'Administrator',
      [EUserRole.TEACHER]: 'Teacher',
      [EUserRole.STUDENT]: 'Student',
      [EUserRole.PARENT]: 'Parent',
    };
    return roleMap[role] || role;
  };

  const getStatusBadge = (status?: string) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', label: 'Active' },
      inactive: { color: 'bg-gray-100 text-gray-800', label: 'Inactive' },
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
      suspended: { color: 'bg-red-100 text-red-800', label: 'Suspended' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.inactive;
    
    return (
      <span className={cn('px-2 py-1 rounded-full text-xs font-medium', config.color)}>
        {config.label}
      </span>
    );
  };

  // Define columns with mobile optimization in mind
  const columns: ColumnDef<any, any>[] = useMemo(() => {
    const cols: ColumnDef<any, any>[] = [
      {
        accessorKey: 'name',
        id: 'name',
        header: entityName.charAt(0).toUpperCase() + entityName.slice(1),
        cell: ({ row }) => (
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center bg-blue-600 text-white flex-shrink-0">
              <UserCircle className="w-5 h-5 md:w-7 md:h-7" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="font-medium text-gray-900 text-sm md:text-base truncate">
                {row.original.name}
              </div>
              <div className="text-xs text-gray-500 truncate md:hidden">
                {row.original.email}
              </div>
            </div>
          </div>
        ),
      },
      {
        accessorKey: 'email',
        id: 'email',
        header: 'Email',
        cell: ({ row }) => (
          <div className="flex items-center gap-2">
            <Mail className="w-4 h-4 text-gray-400 flex-shrink-0" />
            <span className="text-sm text-gray-900 truncate">{row.original.email}</span>
          </div>
        ),
      },
    ];

    if (!hideRoleColumn) {
      cols.push({
        accessorKey: 'role',
        id: 'role',
        header: 'Role',
        cell: ({ row }) => (
          <div className="flex items-center gap-2">
            <Shield className="w-4 h-4 text-gray-400 flex-shrink-0" />
            <span className="text-sm text-gray-900">{getRoleDisplay(row.original.role)}</span>
          </div>
        ),
      });
    }

    if (!hideSchoolColumn) {
      cols.push({
        accessorKey: 'school',
        id: 'school',
        header: 'School',
        cell: ({ row }) => (
          <div className="flex items-center gap-2">
            <School className="w-4 h-4 text-gray-400 flex-shrink-0" />
            <span className="text-sm text-gray-900 truncate">{getSchoolName(row.original.schoolId)}</span>
          </div>
        ),
      });
    }

    cols.push(
      {
        accessorKey: 'status',
        id: 'status',
        header: 'Status',
        cell: ({ row }) => getStatusBadge(row.original.status),
      },
      {
        accessorKey: 'lastActivity',
        id: 'lastActivity',
        header: 'Last Activity',
        cell: ({ row }) => (
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-gray-400 flex-shrink-0" />
            <span className="text-sm text-gray-900">
              {row.original.lastActivity || 'Never'}
            </span>
          </div>
        ),
      },
      {
        accessorKey: 'phone',
        id: 'phone',
        header: 'Phone',
        cell: ({ row }) => (
          <div className="flex items-center gap-2">
            <Phone className="w-4 h-4 text-gray-400 flex-shrink-0" />
            <span className="text-sm text-gray-900">
              {row.original.phone || 'Not provided'}
            </span>
          </div>
        ),
      },
      {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => (
          <div className="flex items-center gap-2">
            {onEditUser && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onEditUser(row.original.id);
                }}
                className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="Edit user"
              >
                <Edit className="w-4 h-4" />
              </button>
            )}
            {onDeleteUser && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteUser(row.original.id);
                }}
                className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                title="Delete user"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            )}
          </div>
        ),
      }
    );

    return cols;
  }, [entityName, hideRoleColumn, hideSchoolColumn, onEditUser, onDeleteUser, getSchoolName]);

  // Define priority columns for mobile (most important info)
  const priorityColumns = ['name', 'role', 'status'];

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="text-red-800 text-sm">{error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">{tableTitle}</h2>
          <p className="text-sm text-gray-600 mt-1">
            {isLoading ? `Loading ${entityNamePlural}...` : `${filteredUsers.length} ${filteredUsers.length === 1 ? entityName : entityNamePlural} found`}
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder={`Search ${entityNamePlural}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div className="flex gap-2">
          <select
            value={filters.role}
            onChange={(e) => setFilters(prev => ({ ...prev, role: e.target.value }))}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Roles</option>
            <option value={EUserRole.ADMIN}>Administrator</option>
            <option value={EUserRole.TEACHER}>Teacher</option>
            <option value={EUserRole.STUDENT}>Student</option>
            <option value={EUserRole.PARENT}>Parent</option>
          </select>
          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="pending">Pending</option>
            <option value="suspended">Suspended</option>
          </select>
        </div>
      </div>

      {/* Table */}
      <MobileOptimizedTable
        columns={columns}
        tableData={filteredUsers}
        isLoading={isLoading}
        priorityColumns={priorityColumns}
        mobileBreakpoint="md"
        showExpandToggle={true}
        mobileCardClassName="hover:shadow-lg"
      />
    </div>
  );
};
