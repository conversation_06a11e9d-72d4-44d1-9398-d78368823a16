import {
  getWorksheetDetail,
  WorksheetGeneratingStatus,
} from '@/apis/worksheet';
import { WorksheetProgressView } from '@/components/molecules/WorksheetProgressView/WorksheetProgressView';
import { ProgressData } from '@/components/molecules/ProgressBar/ProgressBar';
import { onSeverSession } from '@/config/auth';
import { getFileRenderUrl } from '@/utils/fileUtils';

export const WorksheetReview: React.FC<{ id?: string }> = async ({ id }) => {
  if (!id) return <></>;
  const response = await getWorksheetDetail(id);
  if (response.status === 'error') return <div>Something wrong!</div>;

  // Get school information from session
  const ssrSession = await onSeverSession();
  let schoolInfo: {
    name: string;
    address?: string;
    phoneNumber?: string;
    registeredNumber?: string;
    email?: string;
    logoUrl?: string;
  } | undefined = undefined; // Initialize with undefined and provide type
  if (ssrSession?.user?.school) {
    // Get the logo file ID and convert it to a proper render URL
    const logoFileId = ssrSession.user.school.brand?.logo || ssrSession.user.school.brand?.image;
    const logoUrl = logoFileId ? getFileRenderUrl(logoFileId) : undefined;

    schoolInfo = {
      name: ssrSession.user.school.name,
      address: ssrSession.user.school.address,
      phoneNumber: ssrSession.user.school.phoneNumber,
      registeredNumber: ssrSession.user.school.registeredNumber,
      email: ssrSession.user.school.email,
      logoUrl: logoUrl
    };
  }

  // Extract questions and questionIds from the API response
  const initialQuestions = response.data?.promptResult?.result || [];


  // Try multiple possible locations for questionIds
  let initialQuestionIds =
    response.data?.promptResult?.questionIds || // From promptResult
    []; // Fallback to empty array

  // If no questionIds from API, generate them from question.id values
  if (initialQuestionIds.length === 0 && initialQuestions.length > 0) {
    initialQuestionIds = initialQuestions.map(q => q.id).filter(Boolean) as string[];
  }

  // Calculate initial progress based on the number of questions
  // This is a simple estimation since we don't have actual progress data from the API
  const initialProgress: ProgressData = initialQuestions.length > 0 ? {
    current: initialQuestions.length,
    total: Math.max(10, initialQuestions.length), // Assume at least 10 questions total
    percentage: Math.min(100, Math.round((initialQuestions.length / 10) * 100))
  } : {
    current: 0,
    total: 10,
    percentage: 0
  };

  // Extract worksheet information from the response
  const worksheetInfo = {
    topic: response.data?.selectedOptions?.find(s => s?.optionType?.key === 'topic')?.optionValue?.label,
    subject: response.data?.selectedOptions?.find(s => s?.optionType?.key === 'subject')?.optionValue?.label, // Added subject
    grade: response.data?.selectedOptions?.find(s => s?.optionType?.key === 'grade')?.optionValue?.label,
    language: response.data?.selectedOptions?.find(s => s?.optionType?.key === 'language')?.optionValue?.label,
    level: response.data?.selectedOptions?.find(s => s?.optionType?.key === 'level')?.optionValue?.label,
    totalQuestions: initialQuestions.length || initialProgress.total,
  };

  return (
    <WorksheetProgressView
      worksheetId={id}
      initialStatus={response.data?.generatingStatus || WorksheetGeneratingStatus.PENDING}
      initialQuestions={initialQuestions}
      initialQuestionIds={initialQuestionIds}
      initialProgress={initialProgress}
      worksheetInfo={worksheetInfo}
      schoolInfo={schoolInfo}
    />
  );
};
