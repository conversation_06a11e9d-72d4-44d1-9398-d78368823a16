'use server';

import {
  onCreateWorksheet,
  TWorksheet,
  getWorksheets as apiGetWorksheets,
  deleteWorksheetById,
  getWorksheetsEnhanced,
  createWorksheet,
  updateWorksheet,
  getWorksheetDetail,
  addQuestionToWorksheet,
  updateWorksheetQuestion,
  replaceWorksheetQuestion,
  removeQuestionFromWorksheet,
  reorderWorksheetQuestions,
  bulkAddQuestions,
  bulkRemoveQuestions,
  bulkUpdateQuestions,
  getCacheMetrics,
  warmCache
} from "@/apis/worksheet";
import {ERoutes} from "@/config/enums/enum";
import {redirect} from 'next/navigation';
import { revalidatePath } from 'next/cache';
import { TPagination, TPaginationReq } from "@/@types/pagination";
import { TTransformResponse } from "@/apis/transformResponse";
import {
  ICreateWorksheetDto,
  IGetWorksheetsQuery,
  IWorksheetResponse,
  IPaginatedWorksheetsResponse,
  IAddQuestionToWorksheetDto,
  IUpdateWorksheetQuestionDto,
  IReplaceWorksheetQuestionDto,
  IBulkReorderQuestionsDto,
  IBulkAddQuestionsDto,
  IBulkRemoveQuestionsDto,
  IBulkUpdateQuestionsDto,
  IBulkOperationResponseDto,
  ICacheMetrics,
  IServerActionResponse,
  IQuestionResponse
} from "@/@types/worksheet.types";

// ============================================================================
// HELPER TYPES AND FUNCTIONS
// ============================================================================

type TValidationError = {
  field: string;
  constraints: string;
};

/**
 * Helper function to convert API error messages to string array
 */
function formatErrorMessages(message: string | TValidationError[]): string[] {
  if (Array.isArray(message)) {
    // Handle TValidationError[]
    return message.map(error => `${error.field}: ${error.constraints}`);
  }
  // Handle string
  return [message || 'Unknown error'];
}

// ============================================================================
// ENHANCED WORKSHEET SERVER ACTIONS
// ============================================================================

export async function handleGenerateWorksheet(
  data: FormData
): Promise<IServerActionResponse> {
  try {
    const response = await onCreateWorksheet(data);
    if (response.status === 'error') {
      return {
        status: 'error',
        message: 'Failed to create worksheet',
        errors: formatErrorMessages(response.message)
      };
    }

    revalidatePath(ERoutes.MANAGE_WORKSHEET);
    redirect(ERoutes.MANAGE_WORKSHEET);
  } catch (error: any) {
    console.error('Error generating worksheet:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while generating worksheet'
    };
  }
}

export async function createWorksheetAction(
  data: ICreateWorksheetDto
): Promise<IServerActionResponse<IWorksheetResponse>> {
  try {
    const response = await createWorksheet(data);
    if (response.status === 'success') {
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      return {
        status: 'success',
        data: response.data,
        message: 'Worksheet created successfully'
      };
    } else {
      return {
        status: 'error',
        message: 'Failed to create worksheet',
        errors: formatErrorMessages(response.message)
      };
    }
  } catch (error: any) {
    console.error('Error creating worksheet:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while creating worksheet'
    };
  }
}

export async function getWorksheetsAction(
  params: TPaginationReq
): Promise<TTransformResponse<TPagination<TWorksheet>>> {
  return apiGetWorksheets(params);
}

export async function getWorksheetsEnhancedAction(
  query?: IGetWorksheetsQuery
): Promise<TTransformResponse<IPaginatedWorksheetsResponse>> {
  return getWorksheetsEnhanced(query);
}

export async function getWorksheetDetailAction(
  id: string
): Promise<IServerActionResponse<TWorksheet>> {
  try {
    const response = await getWorksheetDetail(id);
    if (response.status === 'success') {
      return {
        status: 'success',
        data: response.data
      };
    } else {
      return {
        status: 'error',
        message: 'Failed to fetch worksheet details',
        errors: formatErrorMessages(response.message)
      };
    }
  } catch (error: any) {
    console.error('Error fetching worksheet details:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching worksheet details'
    };
  }
}

export async function updateWorksheetAction(
  id: string,
  data: Partial<ICreateWorksheetDto>
): Promise<IServerActionResponse<IWorksheetResponse>> {
  try {
    const response = await updateWorksheet(id, data);
    if (response.status === 'success') {
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${id}`);
      return {
        status: 'success',
        data: response.data,
        message: 'Worksheet updated successfully'
      };
    } else {
      return {
        status: 'error',
        message: 'Failed to update worksheet',
        errors: formatErrorMessages(response.message)
      };
    }
  } catch (error: any) {
    console.error('Error updating worksheet:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while updating worksheet'
    };
  }
}

export async function handleDeleteWorksheetAction(
  worksheetId: string
): Promise<IServerActionResponse> {
  try {
    const response = await deleteWorksheetById(worksheetId);
    if (response.status === 'success') {
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      return {
        status: 'success',
        message: response.data?.message || 'Worksheet deleted successfully.'
      };
    } else {
      const errorMessage = response.message || 'Failed to delete worksheet.';
      return {
        status: 'error',
        message: Array.isArray(errorMessage) ? errorMessage.join(', ') : errorMessage
      };
    }
  } catch (error: any) {
    console.error('Error deleting worksheet:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred.'
    };
  }
}

// ============================================================================
// QUESTION MANAGEMENT SERVER ACTIONS
// ============================================================================

export async function addQuestionToWorksheetAction(
  worksheetId: string,
  questionData: IAddQuestionToWorksheetDto
): Promise<IServerActionResponse<IQuestionResponse>> {
  try {
    const response = await addQuestionToWorksheet(worksheetId, questionData);
    if (response.status === 'success') {
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      return {
        status: 'success',
        data: response.data,
        message: 'Question added successfully'
      };
    } else {
      return {
        status: 'error',
        message: 'Failed to add question',
        errors: formatErrorMessages(response.message)
      };
    }
  } catch (error: any) {
    console.error('Error adding question:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while adding question'
    };
  }
}

export async function updateWorksheetQuestionAction(
  worksheetId: string,
  questionId: string,
  updates: IUpdateWorksheetQuestionDto
): Promise<IServerActionResponse<IQuestionResponse>> {
  try {
    const response = await updateWorksheetQuestion(worksheetId, questionId, updates);
    if (response.status === 'success') {
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      return {
        status: 'success',
        data: response.data,
        message: 'Question updated successfully'
      };
    } else {
      return {
        status: 'error',
        message: 'Failed to update question',
        errors: formatErrorMessages(response.message)
      };
    }
  } catch (error: any) {
    console.error('Error updating question:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while updating question'
    };
  }
}

export async function replaceWorksheetQuestionAction(
  worksheetId: string,
  questionId: string,
  questionData: IReplaceWorksheetQuestionDto
): Promise<IServerActionResponse<IQuestionResponse>> {
  try {
    const response = await replaceWorksheetQuestion(worksheetId, questionId, questionData);
    if (response.status === 'success') {
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      return {
        status: 'success',
        data: response.data,
        message: 'Question replaced successfully'
      };
    } else {
      return {
        status: 'error',
        message: 'Failed to replace question',
        errors: formatErrorMessages(response.message)
      };
    }
  } catch (error: any) {
    console.error('Error replacing question:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while replacing question'
    };
  }
}

export async function removeQuestionFromWorksheetAction(
  worksheetId: string,
  questionId: string
): Promise<IServerActionResponse> {
  try {
    const response = await removeQuestionFromWorksheet(worksheetId, questionId);
    if (response.status === 'success') {
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      return {
        status: 'success',
        message: 'Question removed successfully'
      };
    } else {
      return {
        status: 'error',
        message: 'Failed to remove question',
        errors: formatErrorMessages(response.message)
      };
    }
  } catch (error: any) {
    console.error('Error removing question:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while removing question'
    };
  }
}

export async function reorderWorksheetQuestionsAction(
  worksheetId: string,
  reorderData: IBulkReorderQuestionsDto
): Promise<IServerActionResponse<IBulkOperationResponseDto>> {
  try {
    const response = await reorderWorksheetQuestions(worksheetId, reorderData);
    if (response.status === 'success') {
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      return {
        status: 'success',
        data: response.data,
        message: 'Questions reordered successfully'
      };
    } else {
      return {
        status: 'error',
        message: 'Failed to reorder questions',
        errors: formatErrorMessages(response.message)
      };
    }
  } catch (error: any) {
    console.error('Error reordering questions:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while reordering questions'
    };
  }
}

// ============================================================================
// BULK OPERATIONS SERVER ACTIONS
// ============================================================================

export async function bulkAddQuestionsAction(
  worksheetId: string,
  bulkData: IBulkAddQuestionsDto
): Promise<IServerActionResponse<IBulkOperationResponseDto>> {
  try {
    const response = await bulkAddQuestions(worksheetId, bulkData);
    if (response.status === 'success') {
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      return {
        status: 'success',
        data: response.data,
        message: `Bulk add completed: ${response.data?.successCount || 0} questions added successfully`
      };
    } else {
      return {
        status: 'error',
        message: 'Failed to bulk add questions',
        errors: formatErrorMessages(response.message)
      };
    }
  } catch (error: any) {
    console.error('Error bulk adding questions:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while bulk adding questions'
    };
  }
}

export async function bulkRemoveQuestionsAction(
  worksheetId: string,
  bulkData: IBulkRemoveQuestionsDto
): Promise<IServerActionResponse<IBulkOperationResponseDto>> {
  try {
    const response = await bulkRemoveQuestions(worksheetId, bulkData);
    if (response.status === 'success') {
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      return {
        status: 'success',
        data: response.data,
        message: `Bulk remove completed: ${response.data?.successCount || 0} questions removed successfully`
      };
    } else {
      return {
        status: 'error',
        message: 'Failed to bulk remove questions',
        errors: formatErrorMessages(response.message)
      };
    }
  } catch (error: any) {
    console.error('Error bulk removing questions:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while bulk removing questions'
    };
  }
}

export async function bulkUpdateQuestionsAction(
  worksheetId: string,
  bulkData: IBulkUpdateQuestionsDto
): Promise<IServerActionResponse<IBulkOperationResponseDto>> {
  try {
    const response = await bulkUpdateQuestions(worksheetId, bulkData);
    if (response.status === 'success') {
      revalidatePath(ERoutes.MANAGE_WORKSHEET);
      revalidatePath(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${worksheetId}`);
      return {
        status: 'success',
        data: response.data,
        message: `Bulk update completed: ${response.data?.successCount || 0} questions updated successfully`
      };
    } else {
      return {
        status: 'error',
        message: 'Failed to bulk update questions',
        errors: formatErrorMessages(response.message)
      };
    }
  } catch (error: any) {
    console.error('Error bulk updating questions:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while bulk updating questions'
    };
  }
}

// ============================================================================
// CACHE MANAGEMENT SERVER ACTIONS
// ============================================================================

export async function getCacheMetricsAction(): Promise<IServerActionResponse<ICacheMetrics>> {
  try {
    const response = await getCacheMetrics();
    if (response.status === 'success') {
      return {
        status: 'success',
        data: response.data,
        message: 'Cache metrics retrieved successfully'
      };
    } else {
      return {
        status: 'error',
        message: 'Failed to retrieve cache metrics',
        errors: formatErrorMessages(response.message)
      };
    }
  } catch (error: any) {
    console.error('Error retrieving cache metrics:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while retrieving cache metrics'
    };
  }
}

export async function warmCacheAction(): Promise<IServerActionResponse> {
  try {
    const response = await warmCache();
    if (response.status === 'success') {
      return {
        status: 'success',
        message: response.data?.message || 'Cache warmed successfully'
      };
    } else {
      return {
        status: 'error',
        message: 'Failed to warm cache',
        errors: formatErrorMessages(response.message)
      };
    }
  } catch (error: any) {
    console.error('Error warming cache:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while warming cache'
    };
  }
}
