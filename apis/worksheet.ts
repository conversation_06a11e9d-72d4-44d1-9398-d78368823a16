import {EAPIEndpoint} from "@/@types/enums/api";
import {request} from "./request";
import {Question} from "@/components/molecules/QuestionListingView/QuestionListingView";
import {TPagination, TPaginationReq} from "@/@types/pagination";
import { TTransformResponse } from "./transformResponse";
import {
  ICreateWorksheetDto,
  IGetWorksheetsQuery,
  IWorksheetResponse,
  IPaginatedWorksheetsResponse,
  IAddQuestionToWorksheetDto,
  IUpdateWorksheetQuestionDto,
  IReplaceWorksheetQuestionDto,
  IBulkReorderQuestionsDto,
  IBulkAddQuestionsDto,
  IBulkRemoveQuestionsDto,
  IBulkUpdateQuestionsDto,
  IBulkOperationResponseDto,
  ICacheMetrics,
  IDeleteResponse,
  IQuestionResponse,
  IReplaceWithSimilarResponse
} from "@/@types/worksheet.types";

export type TCreateWorksheetReq = {
    options: {key: string, value: string, text?: string}[]
}

export enum WorksheetGeneratingStatus {
    PENDING = 'Pending',
    GENERATED = 'Generated',
    ERROR = 'Error',
}


export type TWorksheet = {
    "id": string;
    "title": string,
    "description": string,
    "generatingStatus": WorksheetGeneratingStatus,
    createdAt: string;
    deletedAt: string;
    "selectedOptions": {
        createdAt: string,
        id: string;
        optionType: {key: string, label: string, description: string;}
        optionValue: {label: string, value: string}
        text: string
    }[]
    promptResult: {result: Question[], questionIds?: string[]} // Add questionIds support
}

// ============================================================================
// ENHANCED WORKSHEET OPERATIONS
// ============================================================================

export const getWorksheets = async (params?: TPaginationReq): Promise<TTransformResponse<TPagination<TWorksheet>>> => {
    let url = `${EAPIEndpoint.WORKSHEETS}`;
    if (params) {
        const queryParams = new URLSearchParams({
            page: params.page.toString(),
            pageSize: params.pageSize.toString(),
        }).toString();
        url = `${url}?${queryParams}`;
    }
    return request<TPagination<TWorksheet>>({url, options: {method: 'GET'}});
}

export const getWorksheetsEnhanced = async (query?: IGetWorksheetsQuery): Promise<TTransformResponse<IPaginatedWorksheetsResponse>> => {
    let url = `${EAPIEndpoint.WORKSHEETS}`;
    if (query) {
        const queryParams = new URLSearchParams();
        Object.entries(query).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
                queryParams.append(key, value.toString());
            }
        });
        const queryString = queryParams.toString();
        if (queryString) {
            url = `${url}?${queryString}`;
        }
    }
    return request<IPaginatedWorksheetsResponse>({url, options: {method: 'GET'}});
}

export const createWorksheet = async (data: ICreateWorksheetDto): Promise<TTransformResponse<IWorksheetResponse>> => {
    const url = `${EAPIEndpoint.WORKSHEETS}`;
    return request<IWorksheetResponse>({
        url,
        options: {
            method: 'POST',
            body: JSON.stringify(data)
        }
    });
}

export const updateWorksheet = async (id: string, data: Partial<ICreateWorksheetDto>): Promise<TTransformResponse<IWorksheetResponse>> => {
    const url = `${EAPIEndpoint.WORKSHEETS}/${id}`;
    return request<IWorksheetResponse>({
        url,
        options: {
            method: 'PATCH',
            body: JSON.stringify(data)
        }
    });
}

export const getWorksheetDetail = async (id: string) => {
    const url = `${EAPIEndpoint.WORKSHEETS}/${id}`;
    return request<TWorksheet>({url, options: {method: 'GET'}});
}

export const deleteWorksheetById = async (worksheetId: string): Promise<TTransformResponse<{ message?: string }>> => {
    const url = `${EAPIEndpoint.WORKSHEETS}/${worksheetId}`;
    return request<{ message?: string }>({ url, options: { method: 'DELETE' } });
};

// ============================================================================
// QUESTION MANAGEMENT OPERATIONS
// ============================================================================

export const addQuestionToWorksheet = async (
    worksheetId: string,
    questionData: IAddQuestionToWorksheetDto
): Promise<TTransformResponse<IQuestionResponse>> => {
    const url = `${EAPIEndpoint.WORKSHEETS}/${worksheetId}/questions`;
    return request<IQuestionResponse>({
        url,
        options: {
            method: 'POST',
            body: JSON.stringify(questionData)
        }
    });
};

export const updateWorksheetQuestion = async (
    worksheetId: string,
    questionId: string,
    updates: IUpdateWorksheetQuestionDto
): Promise<TTransformResponse<IQuestionResponse>> => {
    const url = `${EAPIEndpoint.WORKSHEETS}/${worksheetId}/questions/${questionId}`;
    return request<IQuestionResponse>({
        url,
        options: {
            method: 'PATCH',
            body: JSON.stringify(updates)
        }
    });
};

export const replaceWorksheetQuestion = async (
    worksheetId: string,
    questionId: string,
    questionData: IReplaceWorksheetQuestionDto
): Promise<TTransformResponse<IQuestionResponse>> => {
    const url = `${EAPIEndpoint.WORKSHEETS}/${worksheetId}/questions/${questionId}`;
    return request<IQuestionResponse>({
        url,
        options: {
            method: 'PUT',
            body: JSON.stringify(questionData)
        }
    });
};

export const removeQuestionFromWorksheet = async (
    worksheetId: string,
    questionId: string
): Promise<TTransformResponse<IDeleteResponse>> => {
    const url = `${EAPIEndpoint.WORKSHEETS}/${worksheetId}/questions/${questionId}`;
    return request<IDeleteResponse>({
        url,
        options: { method: 'DELETE' }
    });
};

export const replaceQuestionWithSimilar = async (
    worksheetId: string,
    questionId: string
): Promise<TTransformResponse<IReplaceWithSimilarResponse>> => {
    const url = `${EAPIEndpoint.WORKSHEETS}/${worksheetId}/questions/replace-with-similar/${questionId}`;
    return request<IReplaceWithSimilarResponse>({
        url,
        options: {
            method: 'POST',
            body: JSON.stringify({}) // Empty payload as specified
        }
    });
};

export const reorderWorksheetQuestions = async (
    worksheetId: string,
    reorderData: IBulkReorderQuestionsDto
): Promise<TTransformResponse<IBulkOperationResponseDto>> => {
    const url = `${EAPIEndpoint.WORKSHEETS}/questions/reorder/${worksheetId}`;
    return request<IBulkOperationResponseDto>({
        url,
        options: {
            method: 'PATCH',
            body: JSON.stringify(reorderData)
        }
    });
};

// ============================================================================
// BULK OPERATIONS
// ============================================================================

export const bulkAddQuestions = async (
    worksheetId: string,
    bulkData: IBulkAddQuestionsDto
): Promise<TTransformResponse<IBulkOperationResponseDto>> => {
    const url = `${EAPIEndpoint.WORKSHEETS}/${worksheetId}/questions/bulk-add`;
    return request<IBulkOperationResponseDto>({
        url,
        options: {
            method: 'POST',
            body: JSON.stringify(bulkData)
        }
    });
};

export const bulkRemoveQuestions = async (
    worksheetId: string,
    bulkData: IBulkRemoveQuestionsDto
): Promise<TTransformResponse<IBulkOperationResponseDto>> => {
    const url = `${EAPIEndpoint.WORKSHEETS}/${worksheetId}/questions/bulk-remove`;
    return request<IBulkOperationResponseDto>({
        url,
        options: {
            method: 'DELETE',
            body: JSON.stringify(bulkData)
        }
    });
};

export const bulkUpdateQuestions = async (
    worksheetId: string,
    bulkData: IBulkUpdateQuestionsDto
): Promise<TTransformResponse<IBulkOperationResponseDto>> => {
    const url = `${EAPIEndpoint.WORKSHEETS}/${worksheetId}/questions/bulk-update`;
    return request<IBulkOperationResponseDto>({
        url,
        options: {
            method: 'PATCH',
            body: JSON.stringify(bulkData)
        }
    });
};

// ============================================================================
// CACHE MANAGEMENT OPERATIONS
// ============================================================================

export const getCacheMetrics = async (): Promise<TTransformResponse<ICacheMetrics>> => {
    const url = `${EAPIEndpoint.WORKSHEETS}/cache/metrics`;
    return request<ICacheMetrics>({
        url,
        options: { method: 'GET' }
    });
};

export const warmCache = async (): Promise<TTransformResponse<{ message: string }>> => {
    const url = `${EAPIEndpoint.WORKSHEETS}/cache/warm`;
    return request<{ message: string }>({
        url,
        options: { method: 'GET' }
    });
};

// ============================================================================
// LEGACY COMPATIBILITY EXPORTS
// ============================================================================

// Keep existing exports for backward compatibility
export const onCreateWorksheet = async (data: FormData) => {
    const url = `${EAPIEndpoint.WORKSHEETS}`;
    return request({url, options: {method: 'POST', body: JSON.stringify(data)}});
};
